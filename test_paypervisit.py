#!/usr/bin/env python
"""
Test script for Pay-per-visit POS functionality
This script tests the pay-per-visit transaction processing to ensure it works correctly.
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.test import Client
from django.contrib.auth import get_user_model
from paypervisit.models import PayPerVisit, PayPerVisitSettings
from user.models import MetaData

def test_paypervisit_functionality():
    """Test the pay-per-visit POS functionality"""
    print("🧪 Testing Pay-per-visit POS Functionality")
    print("=" * 50)

    # Create a test client with proper host
    client = Client(HTTP_HOST='127.0.0.1:8000')

    # Get or create a test user with proper permissions
    User = get_user_model()
    test_user, created = User.objects.get_or_create(
        username='test_cashier',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Test',
            'last_name': 'Cashier'
        }
    )
    if created:
        test_user.set_password('testpass123')
        test_user.save()
        print(f"✅ Created test user: {test_user.username}")
    else:
        print(f"✅ Using existing test user: {test_user.username}")

    # Give the user proper permissions for pay-per-visit
    # Set the user's role to admin for testing (admin has full access)
    test_user.role = 'admin'
    test_user.save()
    print("✅ Set test user role to admin for full permissions")

    # Login the test user
    login_success = client.login(username='test_cashier', password='testpass123')
    if not login_success:
        print("❌ Failed to login test user")
        return False
    print("✅ Test user logged in successfully")

    # Check pay-per-visit settings
    settings = PayPerVisitSettings.objects.first()
    if not settings:
        settings = PayPerVisitSettings.objects.create(
            price_per_person=5000,
            quick_select_1=2,
            quick_select_2=5,
            quick_select_3=10,
            price_for_2=10000,
            price_for_5=25000,
            price_for_10=50000
        )
        print("✅ Created pay-per-visit settings")
    else:
        print(f"✅ Pay-per-visit settings found: {settings.price_per_person}៛ per person")

    # Get initial funds
    metadata = MetaData.objects.last()
    if not metadata:
        metadata = MetaData.objects.create(funds=0)
        print("✅ Created metadata with initial funds: 0")
    initial_funds = metadata.funds
    print(f"✅ Initial gym funds: {initial_funds:,}៛")

    # Test 1: Process a single visitor transaction
    print("\n🧪 Test 1: Single visitor transaction")
    transaction_data = {
        'num_people': 1,
        'amount': settings.price_per_person,
        'payment_method': 'cash'
    }

    response = client.post('/paypervisit/', transaction_data)
    if response.status_code == 302:  # Redirect after successful POST
        print("✅ Single visitor transaction processed successfully")

        # Check if transaction was created
        latest_transaction = PayPerVisit.objects.filter(num_people=1).last()
        if latest_transaction:
            print(f"✅ Transaction created: {latest_transaction.trxId}")
            print(f"   Amount: {latest_transaction.amount:,}៛")
            print(f"   Payment method: {latest_transaction.payment_method}")

        # Check if funds were updated
        metadata.refresh_from_db()
        expected_funds = initial_funds + settings.price_per_person
        if metadata.funds == expected_funds:
            print(f"✅ Funds updated correctly: {metadata.funds:,}៛")
        else:
            print(f"❌ Funds not updated correctly. Expected: {expected_funds:,}៛, Got: {metadata.funds:,}៛")
    else:
        print(f"❌ Single visitor transaction failed. Status code: {response.status_code}")
        print(f"Response content: {response.content.decode()[:200]}...")
        return False

    # Test 2: Process a multiple visitor transaction
    print("\n🧪 Test 2: Multiple visitor transaction (2 people)")
    transaction_data = {
        'num_people': 2,
        'amount': settings.price_for_2,
        'payment_method': 'bank'
    }

    current_funds = metadata.funds
    response = client.post('/paypervisit/', transaction_data)
    if response.status_code == 302:
        print("✅ Multiple visitor transaction processed successfully")

        # Check if transaction was created
        latest_transaction = PayPerVisit.objects.filter(num_people=2).last()
        if latest_transaction:
            print(f"✅ Transaction created: {latest_transaction.trxId}")
            print(f"   Amount: {latest_transaction.amount:,}៛")
            print(f"   Payment method: {latest_transaction.payment_method}")

        # Check if funds were updated
        metadata.refresh_from_db()
        expected_funds = current_funds + settings.price_for_2
        if metadata.funds == expected_funds:
            print(f"✅ Funds updated correctly: {metadata.funds:,}៛")
        else:
            print(f"❌ Funds not updated correctly. Expected: {expected_funds:,}៛, Got: {metadata.funds:,}៛")
    else:
        print(f"❌ Multiple visitor transaction failed. Status code: {response.status_code}")
        return False

    # Test 3: Test transaction history page
    print("\n🧪 Test 3: Transaction history page")
    response = client.get('/paypervisit/transaction/')
    if response.status_code == 200:
        print("✅ Transaction history page loads successfully")
        if b'Pay-per-visit Transaction History' in response.content:
            print("✅ Transaction history page contains expected content")
        else:
            print("❌ Transaction history page missing expected content")
    else:
        print(f"❌ Transaction history page failed. Status code: {response.status_code}")
        return False

    print("\n🎉 All Pay-per-visit POS tests passed successfully!")
    print(f"📊 Final gym funds: {metadata.funds:,}៛")
    print(f"📊 Total transactions created: {PayPerVisit.objects.count()}")

    return True

if __name__ == "__main__":
    try:
        success = test_paypervisit_functionality()
        if success:
            print("\n✅ Pay-per-visit POS system is working correctly!")
            sys.exit(0)
        else:
            print("\n❌ Pay-per-visit POS system has issues!")
            sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test failed with exception: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
