{% extends "../base.html" %}

{% load static %}
{% load custom_filters %}
{% load currency_filters %}



{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/product_pos.css' %}">
<link rel="stylesheet" href="{% static 'css/pos_improvements.css' %}">
{% endblock %}

{% block body %}
<div class="conponentSection pos-page-container">

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p class="loading-text">Processing sale...</p>
        </div>
    </div>

    <div class="componentWrapper pos-container">
        <!-- POS Header -->
        <div class="pos-header">
            <div class="flex justify-between items-center mb-4">
                <div>
                    <h2 class="pos-title">Point of Sale</h2>
                    <p class="pos-subtitle">Legend Fitness Club - Product Sales</p>
                </div>
                <div class="flex space-x-3">
                    <a href="{% url 'product:pos_history' %}" class="text-blue-900 hover:underline text-sm">
                        <i class="fas fa-history mr-1"></i> Sales History
                    </a>
                </div>
            </div>
        </div>

        <div class="pos-content">
            <!-- Product Selection Panel -->
            <div class="pos-products-panel">
                <!-- Category Navigation -->
                <div class="category-nav">
                    <div class="category-filters">
                        <button class="category-filter active" data-category="all"><i class="fas fa-th category-filter-icon"></i> All Products</button>
                        {% for category in categories %}
                        <button class="category-filter" data-category="{{ category.id }}"><i class="fas fa-tag category-filter-icon"></i> {{ category.name }}</button>
                        {% endfor %}
                    </div>
                </div>

                <!-- Search Bar -->
                <div class="search-container">
                    <input type="text" id="product-search" placeholder="Search products..." class="form-control">
                    <i class="fas fa-search search-icon"></i>
                    <button type="button" class="search-clear" id="search-clear">&times;</button>
                </div>

                <!-- Product Grid -->
                <div class="product-grid">
                    {% for product in products %}
                    <div class="product-item"
                         data-id="{{ product.id }}"
                         data-name="{{ product.name }}"
                         data-price="{{ product.retail_price }}"
                         data-category="{{ product.category.id|default:'none' }}"
                         data-stock="{{ product.quantity }}"
                         data-threshold="{{ product.low_stock_threshold }}">
                        {% if product.quantity <= product.low_stock_threshold %}
                        <span class="product-badge">Low Stock</span>
                        {% endif %}
                        <div class="product-image-container">
                            {% if product.image %}
                            <img src="{{ product.image.url }}" alt="{{ product.name }}" class="product-image" />
                            {% else %}
                            <div class="product-image bg-gray-200 flex items-center justify-center">
                                <i class="fas fa-box text-gray-400"></i>
                            </div>
                            {% endif %}
                        </div>
                        <div class="product-info">
                            <h3 class="product-name">{{ product.name }}</h3>
                            <div class="product-price">
                                {{ product.retail_price|format_khr }}
                            </div>
                            <div class="product-stock-container">
                                <span class="product-stock {% if product.quantity <= product.low_stock_threshold %}low{% elif product.quantity > 20 %}high{% else %}medium{% endif %}">
                                    Stock: {{ product.quantity }}
                                </span>
                            </div>
                        </div>
                        <!-- Stock level indicator -->
                        <div class="stock-level-indicator">
                            <div class="stock-level-bar {% if product.quantity <= product.low_stock_threshold %}low{% elif product.quantity > 20 %}high{% else %}medium{% endif %}"
                                 style="width: {% if product.quantity > 0 %}{{ product.quantity|floatformat:0 }}%{% else %}0%{% endif %};">
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <div class="col-span-full text-center py-8">
                        <p>No products available for sale.</p>
                    </div>
                    {% endfor %}
                </div>
            </div>

            <!-- Cart Panel -->
            <div class="pos-cart-panel">
                <div class="cart-container">
                    <div class="cart-header">
                        <h3 class="cart-title"><i class="fas fa-shopping-cart cart-title-icon"></i> Current Sale</h3>
                        <p class="cart-subtitle">Add products from the left panel</p>
                    </div>

                    <div class="cart-body">
                        <div id="cart-items" class="cart-items">
                            <div class="cart-empty" id="empty-cart-message">
                                <i class="fas fa-shopping-basket cart-empty-icon"></i>
                                <p class="cart-empty-text">Your cart is empty</p>
                                <p class="cart-empty-subtext">Add products to get started</p>
                            </div>
                            <!-- Cart items will be added here dynamically -->
                        </div>

                        <form method="post" id="saleForm" onsubmit="return validateForm()">
                            {% csrf_token %}

                            <div class="payment-method-group">
                                <label class="form-label"><i class="fas fa-credit-card"></i>Payment Method</label>
                                <select name="payment_method" class="form-select payment-method-select">
                                    <option value="cash">Cash Payment</option>
                                    <option value="bank">Bank Transfer</option>
                                    <option value="other">Other Payment</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label class="form-label"><i class="fas fa-sticky-note"></i>Notes (Optional)</label>
                                <textarea name="notes" class="form-control" rows="2" placeholder="Add any special notes for this sale..."></textarea>
                            </div>

                            <!-- Hidden field to store cart data as JSON -->
                            <input type="hidden" name="cart_data" id="cart-data-input">
                            <div id="hidden-fields-container">
                                <!-- Dynamic hidden fields will be added here -->
                            </div>

                            <div class="cart-actions">
                                <button type="submit" class="btn btn-primary btn-block" id="checkout-btn" disabled><i class="fas fa-check-circle mr-2"></i> Review Sale</button>

                                <button type="button" class="btn btn-danger btn-block" id="clear-cart-btn"><i class="fas fa-trash-alt mr-2"></i> Clear Cart</button>
                            </div>
                        </form>
                    </div>

                    <div class="cart-summary">
                        <div class="cart-total">
                            <div class="cart-total-label">
                                <i class="fas fa-receipt cart-total-icon"></i> Total:
                            </div>
                            <div class="cart-total-value">
                                <span id="cart-total">0៛</span>
                            </div>
                        </div>
                    </div>

                    <!-- Receipt Preview (hidden by default) -->
                    <div id="receipt-preview-container" class="receipt-preview-container" style="display: none;">
                        <div class="receipt-preview">
                            <div class="receipt-paper">
                                <div class="receipt-header">
                                    <div class="receipt-logo">Legend Fitness Club</div>
                                    <h3 class="receipt-title">Sales Receipt</h3>
                                    <p class="receipt-subtitle">Kompongkrobey, Svaypoa, Battambang, Cambodia</p>
                                    <p class="receipt-subtitle">Tel: 070 201 530</p>
                                </div>

                                <div class="receipt-info">
                                    <div class="receipt-info-row">
                                        <span class="receipt-info-label">Date:</span>
                                        <span id="receipt-date"></span>
                                    </div>
                                    <div class="receipt-info-row">
                                        <span class="receipt-info-label">Time:</span>
                                        <span id="receipt-time"></span>
                                    </div>
                                    <div class="receipt-info-row">
                                        <span class="receipt-info-label">Transaction ID:</span>
                                        <span id="receipt-transaction-id"></span>
                                    </div>
                                    <div class="receipt-info-row">
                                        <span class="receipt-info-label">Cashier:</span>
                                        <span id="receipt-cashier">{{ request.user.get_full_name|default:request.user.username }}</span>
                                    </div>
                                </div>

                                <div class="receipt-divider"></div>

                                <div class="receipt-items-header">
                                    <span>Item</span>
                                    <span>Qty</span>
                                    <span>Price</span>
                                    <span>Amount</span>
                                </div>

                                <div class="receipt-container">
                                    <div class="receipt-items" id="receipt-items">
                                        <!-- Receipt items will be added here dynamically -->
                                    </div>
                                </div>

                                <div class="receipt-divider"></div>

                                <div class="receipt-summary">
                                    <div class="receipt-summary-row">
                                        <span class="receipt-summary-label">Subtotal:</span>
                                        <span id="receipt-subtotal">0៛</span>
                                    </div>
                                    <div class="receipt-summary-row">
                                        <span class="receipt-summary-label">Items Count:</span>
                                        <span id="receipt-items-count">0</span>
                                    </div>
                                    <div class="receipt-summary-row">
                                        <span class="receipt-summary-label">Payment Method:</span>
                                        <span id="receipt-payment-method">Cash</span>
                                    </div>
                                </div>

                                <div class="receipt-total">
                                    <span>Total:</span>
                                    <span id="receipt-total">0៛</span>
                                </div>

                                <div class="receipt-footer">
                                    <p class="receipt-footer-message">Thank you for choosing Legend Fitness Club!</p>
                                    <p>Please come again!</p>
                                    <p>Telegram: @LegendFitness</p>
                                </div>
                            </div>
                        </div>

                        <div class="receipt-actions">
                            <button type="button" class="print-button" id="print-receipt-btn"><i class="fas fa-print print-button-icon"></i> Print Receipt</button>

                            <button type="button" class="email-button" id="email-receipt-btn" style="display: none;"><i class="fas fa-envelope print-button-icon"></i> Email Receipt</button>
                        </div>
                    </div>

                    <!-- Keyboard Shortcuts Help -->
                    <div class="keyboard-shortcuts">
                        <h4 class="keyboard-shortcuts-title"><i class="fas fa-keyboard"></i> Keyboard Shortcuts</h4>
                        <div class="keyboard-shortcuts-grid">
                            <div class="keyboard-shortcut-item">
                                <kbd>F</kbd> <span>Search</span>
                            </div>
                            <div class="keyboard-shortcut-item">
                                <kbd>C</kbd> <span>Clear Cart</span>
                            </div>
                            <div class="keyboard-shortcut-item">
                                <kbd>P</kbd> <span>Preview Receipt</span>
                            </div>
                            <div class="keyboard-shortcut-item">
                                <kbd>Enter</kbd> <span>Complete Sale</span>
                            </div>
                            <div class="keyboard-shortcut-item">
                                <kbd>Esc</kbd> <span>Clear Search</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block js %}
<script src="{% static 'js/currency-formatter.js' %}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const cartItems = document.getElementById('cart-items');
        const emptyCartMessage = document.getElementById('empty-cart-message');
        const cartTotal = document.getElementById('cart-total');
        const checkoutBtn = document.getElementById('checkout-btn');
        const clearCartBtn = document.getElementById('clear-cart-btn');
        const productSearch = document.getElementById('product-search');
        const searchClear = document.getElementById('search-clear');
        const receiptPreviewContainer = document.getElementById('receipt-preview-container');
        const printReceiptBtn = document.getElementById('print-receipt-btn');
        const notificationContainer = document.getElementById('notification-container');
        const loadingOverlay = document.getElementById('loading-overlay');

        // Container for hidden fields
        const hiddenFieldsContainer = document.getElementById('hidden-fields-container');

        // Cart data
        let cart = [];

        // Product stock data cache
        let productStockCache = {};

        // Notification System
        function showNotification(type, title, message, duration = 5000) {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;

            let icon = 'info-circle';
            if (type === 'success') icon = 'check-circle';
            if (type === 'error') icon = 'exclamation-circle';
            if (type === 'warning') icon = 'exclamation-triangle';

            // Set background color based on notification type
            if (type === 'success') {
                notification.style.backgroundColor = '#065f46'; // Dark green
            } else if (type === 'error') {
                notification.style.backgroundColor = '#b91c1c'; // Dark red
            } else if (type === 'warning') {
                notification.style.backgroundColor = '#b45309'; // Dark orange
            } else {
                notification.style.backgroundColor = '#1e40af'; // Dark blue
            }

            notification.innerHTML = `
                <i class="fas fa-${icon} notification-icon" style="color: white !important;"></i>
                <div class="notification-content">
                    <div class="notification-title" style="color: white !important; font-weight: bold;">${title}</div>
                    <div class="notification-message" style="color: white !important;">${message}</div>
                </div>
                <button type="button" class="notification-close" style="color: white !important;">&times;</button>
                <div class="notification-progress" style="background-color: rgba(255, 255, 255, 0.3);"></div>
            `;

            notificationContainer.appendChild(notification);

            // Close button functionality
            const closeBtn = notification.querySelector('.notification-close');
            closeBtn.addEventListener('click', () => {
                closeNotification(notification);
            });

            // Auto close after duration
            setTimeout(() => {
                closeNotification(notification);
            }, duration);

            return notification;
        }

        function closeNotification(notification) {
            notification.classList.add('closing');
            setTimeout(() => {
                notification.remove();
            }, 300);
        }

        // Add product to cart with animation and stock validation
        function addToCart(product) {
            // Get the product element and current stock
            const productElement = document.querySelector(`.product-item[data-id="${product.id}"]`);
            const currentStock = parseInt(productElement.getAttribute('data-stock'));

            // Cache the stock data
            productStockCache[product.id] = currentStock;

            // Check if product already in cart
            const existingItem = cart.find(item => item.id === product.id);

            // Calculate new quantity
            const newQuantity = existingItem ? existingItem.quantity + 1 : 1;

            // Check if we have enough stock
            if (newQuantity > currentStock) {
                // Show out of stock notification
                showNotification('error', 'Insufficient Stock', `Only ${currentStock} units of ${product.name} available`, 5000);

                // Add warning animation to product
                productElement.classList.add('shake');
                setTimeout(() => {
                    productElement.classList.remove('shake');
                }, 500);

                return;
            }

            // Add pulse animation to the product item
            productElement.classList.add('pulse');
            setTimeout(() => {
                productElement.classList.remove('pulse');
            }, 500);

            if (existingItem) {
                // Increment quantity if already in cart
                existingItem.quantity += 1;
                showNotification('info', 'Quantity Updated', `Increased ${product.name} quantity to ${existingItem.quantity}`, 3000);
            } else {
                // Add new item to cart
                cart.push({
                    id: product.id,
                    name: product.name,
                    price: parseFloat(product.price),
                    quantity: 1,
                    maxStock: currentStock
                });
                showNotification('success', 'Product Added', `${product.name} added to cart`, 3000);
            }

            updateCartDisplay();

            // Update visual stock indicator
            updateStockVisual(product.id, newQuantity);
        }

        // Update visual stock indicator
        function updateStockVisual(productId, cartQuantity) {
            const productElement = document.querySelector(`.product-item[data-id="${productId}"]`);
            if (!productElement) return;

            const currentStock = parseInt(productElement.getAttribute('data-stock'));
            const threshold = parseInt(productElement.getAttribute('data-threshold'));

            // Calculate remaining stock
            const remainingStock = currentStock - cartQuantity;

            // Update stock display
            const stockDisplay = productElement.querySelector('.product-stock');
            if (stockDisplay) {
                stockDisplay.textContent = `Stock: ${remainingStock}`;

                // Update stock class
                stockDisplay.classList.remove('low', 'medium', 'high');
                if (remainingStock <= threshold) {
                    stockDisplay.classList.add('low');
                } else if (remainingStock > 20) {
                    stockDisplay.classList.add('high');
                } else {
                    stockDisplay.classList.add('medium');
                }
            }

            // Update stock level bar
            const stockLevelBar = productElement.querySelector('.stock-level-bar');
            if (stockLevelBar) {
                const percentRemaining = Math.max(0, (remainingStock / currentStock) * 100);
                stockLevelBar.style.width = `${percentRemaining}%`;

                // Update stock level bar class
                stockLevelBar.classList.remove('low', 'medium', 'high');
                if (remainingStock <= threshold) {
                    stockLevelBar.classList.add('low');
                } else if (remainingStock > 20) {
                    stockLevelBar.classList.add('high');
                } else {
                    stockLevelBar.classList.add('medium');
                }
            }

            // Add warning if stock is critically low
            if (remainingStock <= 1) {
                if (!productElement.querySelector('.stock-warning')) {
                    const warning = document.createElement('div');
                    warning.className = 'stock-warning';
                    warning.innerHTML = '<i class="fas fa-exclamation-triangle stock-warning-icon"></i>';
                    productElement.appendChild(warning);
                }
            } else {
                const warning = productElement.querySelector('.stock-warning');
                if (warning) {
                    warning.remove();
                }
            }
        }

        // Remove product from cart
        function removeFromCart(productId) {
            const item = cart.find(item => item.id === productId);
            if (item) {
                showNotification('warning', 'Product Removed', `${item.name} removed from cart`, 3000);

                // Reset visual stock indicator
                updateStockVisual(productId, 0);
            }

            cart = cart.filter(item => item.id !== productId);
            updateCartDisplay();
        }

        // Update quantity of product in cart with stock validation
        function updateQuantity(productId, quantity) {
            const item = cart.find(item => item.id === productId);
            if (item) {
                const oldQuantity = item.quantity;
                const newQuantity = parseInt(quantity);

                // Get current stock from cache or element
                let currentStock = productStockCache[productId];
                if (currentStock === undefined) {
                    const productElement = document.querySelector(`.product-item[data-id="${productId}"]`);
                    if (productElement) {
                        currentStock = parseInt(productElement.getAttribute('data-stock'));
                        productStockCache[productId] = currentStock;
                    } else {
                        currentStock = item.maxStock || 999; // Fallback
                    }
                }

                // Check if quantity is valid
                if (newQuantity <= 0) {
                    removeFromCart(productId);
                    return;
                }

                // Check if we have enough stock
                if (newQuantity > currentStock) {
                    showNotification('error', 'Insufficient Stock', `Only ${currentStock} units of ${item.name} available`, 5000);

                    // Reset to maximum available
                    item.quantity = currentStock;
                    showNotification('warning', 'Quantity Adjusted', `${item.name} quantity set to maximum available (${currentStock})`, 3000);
                } else {
                    // Update quantity
                    item.quantity = newQuantity;

                    if (oldQuantity !== item.quantity) {
                        showNotification('info', 'Quantity Updated', `${item.name} quantity updated to ${item.quantity}`, 3000);
                    }
                }

                updateCartDisplay();

                // Update visual stock indicator
                updateStockVisual(productId, item.quantity);
            }
        }

        // Update cart display
        function updateCartDisplay() {
            // Clear cart display
            while (cartItems.firstChild) {
                cartItems.removeChild(cartItems.firstChild);
            }

            // Show empty cart message if cart is empty
            if (cart.length === 0) {
                cartItems.appendChild(emptyCartMessage);
                checkoutBtn.disabled = true;
            } else {
                checkoutBtn.disabled = false;

                // Add each item to cart display
                cart.forEach(item => {
                    const itemElement = document.createElement('div');
                    itemElement.className = 'cart-item slide-in';
                    itemElement.innerHTML = `
                        <div class="cart-item-header">
                            <div>
                                <h4 class="cart-item-name">${item.name}</h4>
                                <p class="cart-item-price">
                                    ${formatKHR(item.price)}
                                </p>
                            </div>
                        </div>
                        <div class="cart-item-controls">
                            <div class="quantity-control">
                                <button type="button" class="quantity-btn" data-action="decrease" data-id="${item.id}">-</button>
                                <input type="number" class="quantity-input" value="${item.quantity}" min="1" data-id="${item.id}">
                                <button type="button" class="quantity-btn" data-action="increase" data-id="${item.id}">+</button>
                            </div>
                            <button type="button" class="remove-btn" data-id="${item.id}">×</button>
                        </div>
                        <div class="cart-item-total">
                            ${formatKHR(Math.round(item.price * item.quantity))}
                        </div>
                    `;
                    cartItems.appendChild(itemElement);
                });
            }

            // Update total
            const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            cartTotal.textContent = formatKHR(Math.round(total));

            // Update hidden fields for form submission
            hiddenFieldsContainer.innerHTML = ''; // Clear existing fields

            // Prepare cart data for submission
            const cartData = cart.map(item => ({
                product_id: item.id,
                name: item.name,
                price: item.price,
                quantity: item.quantity,
                total: Math.round(item.price * item.quantity)
            }));

            // Set the cart data as JSON in the hidden input
            document.getElementById('cart-data-input').value = JSON.stringify(cartData);

            // Update receipt preview
            updateReceiptPreview();
        }

        // Update receipt preview with enhanced details
        function updateReceiptPreview() {
            const now = new Date();

            // Set date, time and transaction ID
            document.getElementById('receipt-date').textContent = now.toLocaleDateString();
            document.getElementById('receipt-time').textContent = now.toLocaleTimeString();

            // Generate a more realistic transaction ID with timestamp
            const timestamp = Math.floor(now.getTime() / 1000);
            document.getElementById('receipt-transaction-id').textContent = 'LFC-' + timestamp;

            // Clear receipt items
            const receiptItems = document.getElementById('receipt-items');
            receiptItems.innerHTML = '';

            // Add receipt items with enhanced layout
            cart.forEach(item => {
                const itemElement = document.createElement('div');
                itemElement.className = 'receipt-item';
                itemElement.innerHTML = `
                    <div class="receipt-item-name">${item.name}</div>
                    <div class="receipt-item-qty">${item.quantity}</div>
                    <div class="receipt-item-unit-price">${formatKHR(Math.round(item.price))}</div>
                    <div class="receipt-item-price">${formatKHR(Math.round(item.price * item.quantity))}</div>
                `;
                receiptItems.appendChild(itemElement);
            });

            // Update subtotal, items count, and total
            const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            const itemsCount = cart.reduce((sum, item) => sum + item.quantity, 0);

            document.getElementById('receipt-subtotal').textContent = formatKHR(Math.round(total));
            document.getElementById('receipt-items-count').textContent = itemsCount;
            document.getElementById('receipt-total').textContent = formatKHR(Math.round(total));

            // Update payment method
            const paymentMethod = document.querySelector('select[name="payment_method"]').value;
            let displayText = '';

            // Map payment method values to display text
            switch(paymentMethod) {
                case 'cash':
                    displayText = 'Cash';
                    break;
                case 'bank':
                    displayText = 'Bank Transfer';
                    break;
                case 'other':
                    displayText = 'Other';
                    break;
                default:
                    displayText = paymentMethod.charAt(0).toUpperCase() + paymentMethod.slice(1);
            }

            document.getElementById('receipt-payment-method').textContent = displayText;
        }

        // Add event listeners for product items
        document.querySelectorAll('.product-item').forEach(item => {
            item.addEventListener('click', function() {
                const product = {
                    id: this.getAttribute('data-id'),
                    name: this.getAttribute('data-name'),
                    price: this.getAttribute('data-price')
                };
                addToCart(product);
            });
        });

        // Event delegation for cart item buttons
        cartItems.addEventListener('click', function(e) {
            // Handle remove button
            if (e.target.classList.contains('remove-btn')) {
                const productId = e.target.getAttribute('data-id');
                removeFromCart(productId);
            }

            // Handle quantity buttons
            if (e.target.classList.contains('quantity-btn')) {
                const productId = e.target.getAttribute('data-id');
                const action = e.target.getAttribute('data-action');
                const item = cart.find(item => item.id === productId);

                if (item) {
                    if (action === 'increase') {
                        updateQuantity(productId, item.quantity + 1);
                    } else if (action === 'decrease') {
                        updateQuantity(productId, item.quantity - 1);
                    }
                }
            }
        });

        // Handle quantity input changes
        cartItems.addEventListener('change', function(e) {
            if (e.target.classList.contains('quantity-input')) {
                const productId = e.target.getAttribute('data-id');
                const quantity = e.target.value;
                updateQuantity(productId, quantity);
            }
        });

        // Clear cart button
        clearCartBtn.addEventListener('click', function() {
            if (cart.length > 0) {
                if (confirm('Are you sure you want to clear the cart?')) {
                    cart = [];
                    updateCartDisplay();
                    showNotification('warning', 'Cart Cleared', 'All items have been removed from the cart', 3000);
                }
            }
        });

        // Category filter
        document.querySelectorAll('.category-filter').forEach(button => {
            button.addEventListener('click', function() {
                // Update active button
                document.querySelectorAll('.category-filter').forEach(btn => {
                    btn.classList.remove('active');
                });
                this.classList.add('active');

                const category = this.getAttribute('data-category');
                const categoryName = this.textContent.trim();

                // Filter products
                let visibleCount = 0;
                document.querySelectorAll('.product-item').forEach(item => {
                    if (category === 'all' || item.getAttribute('data-category') === category) {
                        item.classList.remove('hidden');
                        visibleCount++;
                    } else {
                        item.classList.add('hidden');
                    }
                });

                showNotification('info', 'Category Filter', `Showing ${visibleCount} products in ${categoryName}`, 2000);
            });
        });

        // Product search functionality
        productSearch.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase().trim();

            let visibleCount = 0;
            document.querySelectorAll('.product-item').forEach(item => {
                const productName = item.getAttribute('data-name').toLowerCase();

                if (searchTerm === '' || productName.includes(searchTerm)) {
                    item.classList.remove('hidden');
                    visibleCount++;
                } else {
                    item.classList.add('hidden');
                }
            });

            if (searchTerm !== '') {
                showNotification('info', 'Search Results', `Found ${visibleCount} products matching "${searchTerm}"`, 2000);
            }
        });

        // Clear search button
        searchClear.addEventListener('click', function() {
            productSearch.value = '';
            // Trigger the input event to update the product display
            productSearch.dispatchEvent(new Event('input'));
        });

        // Print receipt button
        printReceiptBtn.addEventListener('click', function() {
            window.print();
        });

        // Preview receipt before completing sale
        checkoutBtn.addEventListener('click', function(e) {
            if (validateForm()) {
                e.preventDefault(); // Prevent form submission

                // Show receipt preview
                receiptPreviewContainer.style.display = 'block';

                // Scroll to receipt preview
                receiptPreviewContainer.scrollIntoView({ behavior: 'smooth' });

                // Add a complete sale button to the receipt preview
                const completeBtn = document.createElement('button');
                completeBtn.type = 'button';
                completeBtn.className = 'btn btn-success btn-block mt-3';
                completeBtn.id = 'complete-sale-btn';
                completeBtn.innerHTML = '<i class="fas fa-check-circle mr-2"></i> Complete Sale';

                // Replace print button or add after it
                const printBtn = document.getElementById('print-receipt-btn');
                if (printBtn.nextElementSibling && printBtn.nextElementSibling.id === 'complete-sale-btn') {
                    // Button already exists, don't add again
                } else {
                    printBtn.parentNode.insertBefore(completeBtn, printBtn.nextElementSibling);
                }

                // Add event listener to complete sale button
                completeBtn.addEventListener('click', function() {
                    // Validate stock one more time before submission
                    let stockValid = true;

                    for (const item of cart) {
                        const currentStock = productStockCache[item.id] || 0;
                        if (item.quantity > currentStock) {
                            stockValid = false;
                            showNotification('error', 'Stock Changed', `${item.name} stock has changed. Please refresh the page.`, 5000);
                            break;
                        }
                    }

                    if (!stockValid) {
                        return;
                    }

                    // Show loading overlay
                    loadingOverlay.style.display = 'flex';

                    // Add loading state to button
                    completeBtn.classList.add('btn-loading');

                    // Prevent double submission
                    completeBtn.disabled = true;

                    // Get the form and submit it to the server
                    const saleForm = document.getElementById('saleForm');

                    // Add transaction success animation
                    const successAnimation = document.createElement('div');
                    successAnimation.className = 'transaction-success';
                    successAnimation.innerHTML = `
                        <div class="success-checkmark">
                            <i class="fas fa-check"></i>
                        </div>
                    `;

                    // Add to body after a delay
                    setTimeout(() => {
                        document.body.appendChild(successAnimation);

                        // Remove after animation completes
                        setTimeout(() => {
                            successAnimation.remove();
                        }, 3000);
                    }, 1000);

                    // Actually submit the form to the server
                    saleForm.submit();
                });

                showNotification('info', 'Review Order', 'Please review the receipt before completing the sale', 5000);
            }
        });

        // Initialize cart display
        updateCartDisplay();

        // Event listener for payment method change
        const paymentMethodSelect = document.querySelector('select[name="payment_method"]');
        paymentMethodSelect.addEventListener('change', function() {
            // Update receipt payment method display
            const selectedValue = this.value;
            let displayText = '';

            // Map payment method values to display text
            switch(selectedValue) {
                case 'cash':
                    displayText = 'Cash';
                    break;
                case 'bank':
                    displayText = 'Bank Transfer';
                    break;
                case 'other':
                    displayText = 'Other';
                    break;
                default:
                    displayText = selectedValue.charAt(0).toUpperCase() + selectedValue.slice(1);
            }

            document.getElementById('receipt-payment-method').textContent = displayText;

            // Show notification about payment method change
            showNotification('info', 'Payment Method Updated', `Payment method changed to ${displayText}`, 2000);
        });

        // Form validation function
        function validateForm() {
            if (cart.length === 0) {
                showNotification('error', 'Empty Cart', 'Please add products before completing the sale', 5000);
                return false;
            }
            return true;
        }

        // Handle form submission - show receipt preview first, then submit on complete button click
        document.getElementById('saleForm').addEventListener('submit', function(e) {
            // If the receipt preview is already showing, allow the form to submit normally
            if (receiptPreviewContainer.style.display === 'block') {
                return true;
            }

            // Otherwise prevent default submission and show the receipt preview first
            e.preventDefault();

            // Show receipt preview instead of submitting directly
            if (validateForm()) {
                // Trigger the click event on the checkout button to show receipt preview
                checkoutBtn.click();
            }

            return false;
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Focus search box with F key
            if (e.key === 'f' && !e.ctrlKey && !e.altKey) {
                e.preventDefault();
                productSearch.focus();
            }

            // Clear cart with C key
            if (e.key === 'c' && !e.ctrlKey && !e.altKey) {
                e.preventDefault();
                if (cart.length > 0) {
                    if (confirm('Are you sure you want to clear the cart?')) {
                        cart = [];
                        updateCartDisplay();
                        showNotification('warning', 'Cart Cleared', 'All items have been removed from the cart', 3000);
                    }
                }
            }

            // Preview receipt with P key
            if (e.key === 'p' && !e.ctrlKey && !e.altKey) {
                e.preventDefault();
                if (validateForm()) {
                    // Trigger the click event on the checkout button to show receipt preview
                    checkoutBtn.click();
                }
            }

            // Complete sale with Enter key when receipt is visible
            if (e.key === 'Enter' && receiptPreviewContainer.style.display === 'block') {
                e.preventDefault();
                const completeBtn = document.getElementById('complete-sale-btn');
                if (completeBtn) {
                    completeBtn.click();
                }
            }

            // Clear search with Escape key
            if (e.key === 'Escape') {
                e.preventDefault();
                productSearch.value = '';
                productSearch.dispatchEvent(new Event('input'));
                productSearch.blur();
            }
        });

        // Check for Django messages and convert them to notifications
        {% if messages %}
            {% for message in messages %}
                {% if 'success' in message.tags %}
                    setTimeout(() => {
                        showNotification('success', 'Success', '{{ message }}', 7000);
                    }, 500);
                {% elif 'error' in message.tags %}
                    setTimeout(() => {
                        showNotification('error', 'Error', '{{ message }}', 7000);
                    }, 500);
                {% else %}
                    setTimeout(() => {
                        showNotification('info', 'Information', '{{ message }}', 7000);
                    }, 500);
                {% endif %}
            {% endfor %}
        {% endif %}

        // Show welcome notification
        setTimeout(() => {
            showNotification('info', 'Welcome to POS System', 'Use the search bar or category filters to find products', 5000);
        }, 1500);
    });
</script>
{% endblock %}
